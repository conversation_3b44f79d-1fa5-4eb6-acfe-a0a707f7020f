# Sinoair Agent 功能详细设计文档

## 1. 项目概述

### 1.1 项目名称
Sinoair Agent - 国际航空货运代理智能识别与回填系统

### 1.2 项目背景
在国际航空货运代理行业中，业务人员需要处理大量的单证文件（图片、PDF等格式），这些文件虽然格式不同，但内容都遵循IATA（国际航空运输协会）规范。传统的人工录入方式效率低下且容易出错。

本项目利用LLM大模型的图像识别和文本理解能力，自动识别各类航空货运单证，提取结构化数据，并通过自动化技术回填到目标业务系统中，大幅提升工作效率和准确性。

### 1.3 项目目标
- 通过LLM识别各类航空货运单证的图片、PDF等文件
- 按照预定义的Agent提示词要求，返回标准化的JSON格式数据
- 利用puppeteer、playwright等自动化技术，将提取的数据回写到目标网页系统
- 提供完整的Agent管理和使用平台

## 2. 系统架构

### 2.1 系统角色定义

#### 2.1.1 系统管理员
**职责范围：**
- 用户账户管理（创建、删除、权限分配）
- 大模型配置管理（API密钥、模型选择、参数调优）
- 回写网站配置管理（目标网站信息、认证配置）
- 系统日志查看和分析
- 系统性能监控和维护

#### 2.1.2 航空代理员工
**职责范围：**
- Agent创建和配置
- Agent发布和版本管理
- Agent调用和测试
- Agent迭代优化和升级
- 使用Agent处理业务单证
- 监控回写结果和异常处理
- Agent的使用

### 2.2 系统功能模块

```mermaid
graph LR
    A[Sinoair Agent 系统] --> B[管理员]
    A --> C[航空代理员工]


    B --> B1[用户管理]
    B --> B2[大模型管理]
    B --> B3[回填网站管理]
    B --> B4[日志查看]


    C --> C1[Agent管理]
    C --> C2[Agent使用]


    C1 --> C1.1[Agent创建和维护]
    C1 --> C1.2[Prompt调试]
    C1 --> C1.3[回填网站绑定]
    C1 --> C1.4[历史查看]
```

## 3. 详细功能设计

### 3.1 系统设置模块

#### 3.1.1 用户管理
**功能描述：** 管理系统用户账户和权限

**主要功能：**
- 用户注册和登录
- 用户信息管理（姓名、邮箱、部门、角色）
- 角色权限配置（系统管理员、普通用户）
- 用户状态管理（启用、禁用、锁定）
- 密码策略和安全设置

**数据结构：**
```json
{
  "user": {
    "id": "用户ID",
    "username": "用户名",
    "password": "密码",
    "email": "邮箱",
    "role": "角色（admin/user）",
    "department": "部门",
    "status": "状态（active/inactive/locked）",
    "created_at": "创建时间",
    "last_login": "最后登录时间"
  }
}
```

#### 3.2.1 大模型管理

**功能描述：** 管理和配置LLM

**主要功能：**

- 创建、编辑大模型信息
- 关键信息（LLM的名称、CODE、描述、状态、创建时间）
- 状态管理（启用、禁用）

  

#### 3.3.1 回填网站管理

**功能描述：** 管理和配置回填网站的信息
**主要功能：**

- 创建、编辑回填网站的信息
- 关键信息（回填网站的名称、CODE、描述、状态、创建时间、配置）
- 状态管理（启用、禁用）

  

#### 3.4.1 日志查看

**功能描述：** 查看系统的使用日志

**主要功能：**

- 关键信息（日志id、操作人、操作功能、操作参数、创建时间）

  

#### 3.5.1 Agent维护

**功能描述：** Agent的全生命周期管理

**主要功能：**

- Agent创建和基本信息配置
- Agent分类管理（按单证类型分类）
- Agent状态管理（已发布、已经下线）
- Agent权限控制（创建者、使用者权限）

**Agent基本信息：**

```json
{
  "agent": {
    "id": "Agent ID",
    "name": "Agent名称",
    "description": "Agent描述",
    "category": "分类（AWB/发票/装箱单等）",
    "version": "版本号",
    "status": "状态",
    "creator": "创建者",
    "created_at": "创建时间",
    "updated_at": "更新时间",
    "prompt":"提示词"
  }
}
```

#### 3.2.2 Prompt调试
**功能描述：** Agent提示词的编辑、测试和优化

**主要功能：**

- 提示词编辑器（支持语法高亮）
- 测试文件上传和预览
- 实时调试和结果预览
- 提示词版本对比
- 性能指标监控（准确率、响应时间）

**提示词结构：**
```json
{
  "prompt": {
    "system_prompt": "系统提示词",
    "user_prompt": "用户提示词模板",
    "output_schema": "输出JSON格式定义",
    "examples": "示例数据",
    "validation_rules": "验证规则"
  }
}
```

#### 3.2.3 历史记录
**功能描述：** Agent使用历史和结果追踪

**主要功能：**
- 处理历史记录查看
- 输入文件和输出结果对比
- 错误记录和分析
- 处理结果评价和反馈
- 数据导出功能

### 3.3 回填设置模块

#### 3.3.1 网站配置
**功能描述：** 目标回写网站的配置管理

**主要功能：**
- 网站基本信息配置（URL、名称、描述）
- 登录认证配置（用户名密码、Token等）
- 页面元素定位配置
- 网站访问测试和验证

**配置结构：**
```json
{
  "website": {
    "id": "网站ID",
    "name": "网站名称",
    "url": "网站URL",
    "auth_type": "认证类型",
    "auth_config": "认证配置",
    "selectors": "页面元素选择器配置"
  }
}
```

### 3.4 Agent使用模块

#### 3.4.1 Web界面
**功能描述：** 基于Web的Agent使用界面

**主要功能：**
- 文件上传（支持图片、PDF等格式）
- Agent选择和配置
- 实时处理进度显示
- 结果预览和编辑
- 一键回写功能

**使用流程：**
1. 选择对应的Agent
2. 上传需要处理的文件
3. 系统自动识别和提取数据
4. 用户确认和编辑结果
5. 选择目标网站进行回写

#### 3.4.2 浏览器插件
**功能描述：** 浏览器插件形式的Agent使用工具

**主要功能：**
- 插件安装和配置
- 页面内文件识别
- 右键菜单快速调用
- 结果直接回填到当前页面


## 5. 数据流程

### 5.1 文件处理流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant W as Web界面
    participant A as Agent服务
    participant L as LLM服务
    participant D as 数据库
    
    U->>W: 上传文件
    W->>A: 发送处理请求
    A->>L: 调用LLM识别
    L->>A: 返回JSON结果
    A->>D: 保存处理记录
    A->>W: 返回处理结果
    W->>U: 显示结果
```

### 5.2 回写流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant W as Web界面
    participant R as 回写服务
    participant T as 目标网站
    
    U->>W: 确认回写
    W->>R: 发送回写请求
    R->>T: 自动化登录
    R->>T: 定位页面元素
    R->>T: 填写数据
    R->>W: 返回回写结果
    W->>U: 显示回写状态
```

## 8. 业务流程详细设计

### 8.1 Agent创建流程
```mermaid
flowchart TD
    A[开始创建Agent] --> B[填写基本信息]
    B --> C[选择单证类型]
    C --> D[编写系统提示词]
    D --> E[定义输出格式]
    E --> F[上传测试样本]
    F --> G[调试测试]
    G --> H{测试通过?}
    H -->|否| I[修改提示词]
    I --> G
    H -->|是| J[保存Agent]
    J --> K[发布Agent]
    K --> L[结束]
```

### 8.2 文件处理业务流程
```mermaid
flowchart TD
    A[用户上传文件] --> B[文件格式验证]
    B --> C{格式正确?}
    C -->|否| D[返回错误信息]
    C -->|是| E[文件预处理]
    E --> F[选择对应Agent]
    F --> G[调用LLM识别]
    G --> H[结果验证]
    H --> I{验证通过?}
    I -->|否| J[人工校验]
    I -->|是| K[格式化输出]
    J --> K
    K --> L[保存结果]
    L --> M[用户确认]
    M --> N{需要回写?}
    N -->|是| O[执行回写]
    N -->|否| P[完成处理]
    O --> P
```