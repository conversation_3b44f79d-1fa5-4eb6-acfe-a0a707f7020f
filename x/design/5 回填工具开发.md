# Sinoair Agent 回填工具开发设计文档

## 1. 项目概述

### 1.1 设计目标
设计两个回填工具组件，实现Agent解析文件后自动回填到目标网站的功能：
1. **PC客户端工具** - 独立的桌面应用程序，通过API与后端交互
2. **浏览器插件** - 集成在浏览器中的扩展程序，提供便捷的回填操作

### 1.2 技术架构
- **PC客户端**: Electron + Vue 3 + TypeScript
- **浏览器插件**: Manifest V3 + Vue 3 + TypeScript
- **通信协议**: RESTful API + WebSocket
- **自动化引擎**: Puppeteer/Playwright

## 2. PC客户端设计

### 2.1 架构设计

#### 2.1.1 技术栈
```
PC客户端技术栈:
├── Electron 28.x          # 跨平台桌面应用框架
├── Vue 3.5.x              # 前端框架
├── TypeScript 5.x         # 类型安全
├── Element Plus 2.x       # UI组件库
├── Pinia                  # 状态管理
├── Axios                  # HTTP客户端
├── Socket.io-client       # WebSocket通信
└── Node.js APIs           # 系统级功能
```

#### 2.1.2 应用架构
```
src/
├── main/                  # 主进程
│   ├── index.ts          # 应用入口
│   ├── window.ts         # 窗口管理
│   ├── menu.ts           # 菜单配置
│   ├── ipc.ts            # 进程间通信
│   └── updater.ts        # 自动更新
├── renderer/             # 渲染进程
│   ├── components/       # 组件
│   ├── views/           # 页面
│   ├── stores/          # 状态管理
│   ├── services/        # 业务服务
│   ├── utils/           # 工具函数
│   └── types/           # 类型定义
├── shared/              # 共享代码
│   ├── constants.ts     # 常量定义
│   ├── types.ts         # 共享类型
│   └── utils.ts         # 共享工具
└── preload/             # 预加载脚本
    └── index.ts         # 安全的API暴露
```

### 2.2 核心功能模块

#### 2.2.1 连接管理模块
**功能描述：** 管理与Sinoair Agent后端服务的连接

**主要功能：**
- 服务器连接配置和测试
- 用户认证和会话管理
- 连接状态监控和自动重连
- API请求封装和错误处理

**实现设计：**
```typescript
// 连接管理服务
class ConnectionService {
  private apiClient: AxiosInstance;
  private socketClient: Socket;
  private config: ConnectionConfig;

  async connect(config: ConnectionConfig): Promise<boolean> {
    try {
      // 1. 验证服务器连接
      await this.testConnection(config.serverUrl);

      // 2. 用户认证
      const authResult = await this.authenticate(config.credentials);

      // 3. 建立WebSocket连接
      await this.connectWebSocket(authResult.token);

      // 4. 保存配置
      this.saveConfig(config);

      return true;
    } catch (error) {
      throw new ConnectionError(`连接失败: ${error.message}`);
    }
  }

  async testConnection(serverUrl: string): Promise<void> {
    const response = await axios.get(`${serverUrl}/api/health`);
    if (response.status !== 200) {
      throw new Error('服务器不可用');
    }
  }

  async authenticate(credentials: UserCredentials): Promise<AuthResult> {
    const response = await this.apiClient.post('/api/auth/login', credentials);
    return response.data;
  }
}
```

#### 2.2.2 任务监控模块
**功能描述：** 监控Agent处理任务和回填任务的状态

**主要功能：**
- 实时监控处理任务队列
- 显示任务进度和状态
- 任务结果通知和提醒
- 历史任务记录查看

**实现设计：**
```typescript
// 任务监控服务
class TaskMonitorService {
  private tasks: Map<string, TaskInfo> = new Map();
  private eventBus: EventEmitter;

  async startMonitoring(): Promise<void> {
    // 1. 订阅WebSocket事件
    this.socketClient.on('task:created', this.handleTaskCreated.bind(this));
    this.socketClient.on('task:updated', this.handleTaskUpdated.bind(this));
    this.socketClient.on('task:completed', this.handleTaskCompleted.bind(this));

    // 2. 定期同步任务状态
    setInterval(() => this.syncTaskStatus(), 30000);
  }

  private handleTaskCreated(task: TaskInfo): void {
    this.tasks.set(task.id, task);
    this.eventBus.emit('task:new', task);

    // 显示系统通知
    this.showNotification(`新任务: ${task.fileName}`, 'info');
  }

  private handleTaskCompleted(task: TaskInfo): void {
    this.tasks.set(task.id, task);
    this.eventBus.emit('task:completed', task);

    // 显示完成通知
    this.showNotification(`任务完成: ${task.fileName}`, 'success');

    // 自动触发回填（如果配置了）
    if (task.autoFillback && task.status === 'completed') {
      this.triggerAutoFillback(task);
    }
  }
}
```

#### 2.2.3 回填执行模块
**功能描述：** 执行自动化回填操作

**主要功能：**
- 选择目标网站和配置
- 数据映射和验证
- 执行回填操作
- 结果验证和错误处理

**实现设计：**
```typescript
// 回填执行服务
class FillbackExecutorService {
  private puppeteerService: PuppeteerService;

  async executeFillback(request: FillbackRequest): Promise<FillbackResult> {
    try {
      // 1. 获取网站配置
      const websiteConfig = await this.getWebsiteConfig(request.websiteId);

      // 2. 数据映射
      const mappedData = this.mapData(request.data, websiteConfig.fieldMappings);

      // 3. 启动浏览器
      const browser = await this.puppeteerService.launch({
        headless: request.headless ?? true,
        devtools: !request.headless
      });

      // 4. 执行回填脚本
      const result = await this.runFillbackScript(browser, websiteConfig, mappedData);

      // 5. 保存结果
      await this.saveFillbackResult(request.taskId, result);

      return result;
    } catch (error) {
      throw new FillbackError(`回填失败: ${error.message}`);
    }
  }

  private async runFillbackScript(
    browser: Browser,
    config: WebsiteConfig,
    data: MappedData
  ): Promise<FillbackResult> {
    const page = await browser.newPage();
    const steps: FillbackStep[] = [];

    try {
      // 1. 导航到目标页面
      await page.goto(config.url, { waitUntil: 'networkidle2' });
      steps.push({ name: 'navigate', status: 'completed', duration: 1000 });

      // 2. 执行登录（如果需要）
      if (config.authRequired) {
        await this.performLogin(page, config.authConfig);
        steps.push({ name: 'login', status: 'completed', duration: 2000 });
      }

      // 3. 填写表单
      await this.fillForm(page, data, config.selectors);
      steps.push({ name: 'fillForm', status: 'completed', duration: 3000 });

      // 4. 提交表单（可选）
      if (config.autoSubmit) {
        await this.submitForm(page, config.submitSelector);
        steps.push({ name: 'submit', status: 'completed', duration: 1000 });
      }

      // 5. 截图保存
      const screenshot = await page.screenshot({ fullPage: true });

      return {
        success: true,
        steps,
        screenshot: screenshot.toString('base64'),
        executionTime: steps.reduce((sum, step) => sum + step.duration, 0)
      };
    } finally {
      await page.close();
      await browser.close();
    }
  }

  private async fillForm(page: Page, data: MappedData, selectors: FieldSelectors): Promise<void> {
    for (const [fieldName, value] of Object.entries(data)) {
      const selector = selectors[fieldName];
      if (!selector || !value) continue;

      try {
        await page.waitForSelector(selector, { timeout: 5000 });

        // 根据元素类型执行不同操作
        const elementType = await page.evaluate((sel) => {
          const element = document.querySelector(sel);
          return element?.tagName.toLowerCase();
        }, selector);

        switch (elementType) {
          case 'input':
            await page.type(selector, value.toString());
            break;
          case 'select':
            await page.select(selector, value.toString());
            break;
          case 'textarea':
            await page.type(selector, value.toString());
            break;
          default:
            await page.click(selector);
        }

        // 等待一下确保输入完成
        await page.waitForTimeout(500);
      } catch (error) {
        console.warn(`填写字段 ${fieldName} 失败:`, error.message);
      }
    }
  }
}
```

### 2.3 用户界面设计

#### 2.3.1 主界面布局
```vue
<template>
  <div class="app-container">
    <!-- 顶部工具栏 -->
    <header class="app-header">
      <div class="connection-status">
        <el-badge :value="connectionStatus" :type="statusType">
          <el-button @click="showConnectionDialog">连接设置</el-button>
        </el-badge>
      </div>
      <div class="user-info">
        <el-dropdown @command="handleUserCommand">
          <span class="user-name">{{ userInfo.name }}</span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人设置</el-dropdown-item>
              <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="app-main">
      <!-- 左侧导航 -->
      <aside class="app-sidebar">
        <el-menu :default-active="activeMenu" @select="handleMenuSelect">
          <el-menu-item index="tasks">
            <el-icon><Document /></el-icon>
            <span>任务监控</span>
          </el-menu-item>
          <el-menu-item index="fillback">
            <el-icon><Upload /></el-icon>
            <span>回填操作</span>
          </el-menu-item>
          <el-menu-item index="history">
            <el-icon><Clock /></el-icon>
            <span>历史记录</span>
          </el-menu-item>
          <el-menu-item index="settings">
            <el-icon><Setting /></el-icon>
            <span>设置</span>
          </el-menu-item>
        </el-menu>
      </aside>

      <!-- 内容区域 -->
      <section class="app-content">
        <router-view />
      </section>
    </main>

    <!-- 状态栏 -->
    <footer class="app-footer">
      <div class="status-info">
        <span>任务队列: {{ taskQueue.length }}</span>
        <span>运行状态: {{ runningStatus }}</span>
      </div>
    </footer>
  </div>
</template>
```

#### 2.3.2 任务监控界面
```vue
<template>
  <div class="task-monitor">
    <!-- 任务统计 -->
    <div class="task-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card>
            <div class="stat-item">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总任务数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card>
            <div class="stat-item">
              <div class="stat-value">{{ stats.processing }}</div>
              <div class="stat-label">处理中</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card>
            <div class="stat-item">
              <div class="stat-value">{{ stats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card>
            <div class="stat-item">
              <div class="stat-value">{{ stats.failed }}</div>
              <div class="stat-label">失败</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 任务列表 -->
    <div class="task-list">
      <el-table :data="tasks" v-loading="loading">
        <el-table-column prop="fileName" label="文件名" />
        <el-table-column prop="agentName" label="Agent" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度">
          <template #default="{ row }">
            <el-progress :percentage="row.progress" />
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" />
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button size="small" @click="viewResult(row)">查看结果</el-button>
            <el-button
              size="small"
              type="primary"
              @click="triggerFillback(row)"
              :disabled="row.status !== 'completed'"
            >
              回填
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
```

### 2.4 API接口设计

#### 2.4.1 连接和认证接口
```typescript
// 连接测试
interface HealthCheckResponse {
  status: 'ok' | 'error';
  version: string;
  timestamp: number;
}

// 用户认证
interface LoginRequest {
  username: string;
  password: string;
}

interface LoginResponse {
  success: boolean;
  token: string;
  user: UserInfo;
  expiresIn: number;
}

// 任务查询
interface TaskListRequest {
  page: number;
  size: number;
  status?: TaskStatus;
  userId?: number;
}

interface TaskListResponse {
  success: boolean;
  data: {
    tasks: TaskInfo[];
    total: number;
    page: number;
    size: number;
  };
}
```

#### 2.4.2 回填操作接口
```typescript
// 回填请求
interface FillbackRequest {
  taskId: string;
  websiteId: number;
  data: Record<string, any>;
  options?: {
    headless?: boolean;
    autoSubmit?: boolean;
    screenshot?: boolean;
  };
}

// 回填响应
interface FillbackResponse {
  success: boolean;
  fillbackId: string;
  message?: string;
}

// 回填状态查询
interface FillbackStatusResponse {
  success: boolean;
  data: {
    id: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress: number;
    steps: FillbackStep[];
    result?: FillbackResult;
    error?: string;
  };
}
```

## 3. 浏览器插件设计

### 3.1 插件架构设计

#### 3.1.1 Manifest V3 配置
```json
{
  "manifest_version": 3,
  "name": "Sinoair Agent 回填助手",
  "version": "1.0.0",
  "description": "智能识别和自动回填航空货运单证",
  "permissions": [
    "activeTab",
    "storage",
    "scripting",
    "notifications"
  ],
  "host_permissions": [
    "http://localhost:*/*",
    "https://*.sinoair.com/*"
  ],
  "background": {
    "service_worker": "background.js"
  },
  "content_scripts": [
    {
      "matches": ["<all_urls>"],
      "js": ["content.js"],
      "css": ["content.css"]
    }
  ],
  "action": {
    "default_popup": "popup.html",
    "default_title": "Sinoair Agent"
  },
  "web_accessible_resources": [
    {
      "resources": ["inject.js"],
      "matches": ["<all_urls>"]
    }
  ]
}
```

#### 3.1.2 插件文件结构
```
extension/
├── manifest.json         # 插件配置
├── background.js         # 后台脚本
├── content.js           # 内容脚本
├── inject.js            # 注入脚本
├── popup/               # 弹窗界面
│   ├── popup.html
│   ├── popup.js
│   └── popup.css
├── options/             # 设置页面
│   ├── options.html
│   ├── options.js
│   └── options.css
├── assets/              # 静态资源
│   ├── icons/
│   └── images/
└── utils/               # 工具函数
    ├── api.js
    ├── storage.js
    └── common.js
```

### 3.2 核心功能模块

#### 3.2.1 后台脚本 (background.js)
```javascript
// 后台服务工作者
class BackgroundService {
  constructor() {
    this.apiClient = new ApiClient();
    this.init();
  }

  init() {
    // 监听插件安装
    chrome.runtime.onInstalled.addListener(this.handleInstalled.bind(this));

    // 监听消息
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));

    // 监听标签页更新
    chrome.tabs.onUpdated.addListener(this.handleTabUpdated.bind(this));

    // 定期同步数据
    this.startPeriodicSync();
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'GET_TASKS':
          const tasks = await this.apiClient.getTasks();
          sendResponse({ success: true, data: tasks });
          break;

        case 'EXECUTE_FILLBACK':
          const result = await this.executeFillback(request.data);
          sendResponse({ success: true, data: result });
          break;

        case 'GET_WEBSITE_CONFIG':
          const config = await this.getWebsiteConfig(request.url);
          sendResponse({ success: true, data: config });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      sendResponse({ success: false, error: error.message });
    }

    return true; // 保持消息通道开放
  }

  async executeFillback(data) {
    // 获取当前活动标签页
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    // 向内容脚本发送回填指令
    const result = await chrome.tabs.sendMessage(tab.id, {
      action: 'FILLBACK_DATA',
      data: data
    });

    return result;
  }

  startPeriodicSync() {
    // 每30秒同步一次任务状态
    setInterval(async () => {
      try {
        const tasks = await this.apiClient.getTasks();
        await this.updateBadge(tasks);
      } catch (error) {
        console.error('同步失败:', error);
      }
    }, 30000);
  }

  async updateBadge(tasks) {
    const pendingCount = tasks.filter(t => t.status === 'completed').length;
    const badgeText = pendingCount > 0 ? pendingCount.toString() : '';

    await chrome.action.setBadgeText({ text: badgeText });
    await chrome.action.setBadgeBackgroundColor({ color: '#4CAF50' });
  }
}

// 初始化后台服务
new BackgroundService();
```

#### 3.2.2 内容脚本 (content.js)
```javascript
// 内容脚本 - 在网页中运行
class ContentScript {
  constructor() {
    this.fillbackOverlay = null;
    this.init();
  }

  init() {
    // 监听来自后台脚本的消息
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));

    // 检测页面是否支持回填
    this.detectFillbackSupport();

    // 添加右键菜单支持
    this.addContextMenuSupport();
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'FILLBACK_DATA':
          const result = await this.fillbackData(request.data);
          sendResponse({ success: true, data: result });
          break;

        case 'DETECT_FORM':
          const formInfo = this.detectForm();
          sendResponse({ success: true, data: formInfo });
          break;

        case 'HIGHLIGHT_FIELDS':
          this.highlightFields(request.selectors);
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      sendResponse({ success: false, error: error.message });
    }

    return true;
  }

  async fillbackData(data) {
    try {
      // 显示回填进度覆盖层
      this.showFillbackOverlay();

      // 获取网站配置
      const config = await this.getWebsiteConfig();

      // 执行字段映射和填写
      const results = [];
      for (const [fieldName, value] of Object.entries(data)) {
        const selector = config.selectors[fieldName];
        if (selector && value) {
          const result = await this.fillField(selector, value);
          results.push({ field: fieldName, success: result });
        }
      }

      // 隐藏覆盖层
      this.hideFillbackOverlay();

      // 显示完成通知
      this.showNotification('回填完成', 'success');

      return { results, totalFields: Object.keys(data).length };
    } catch (error) {
      this.hideFillbackOverlay();
      this.showNotification(`回填失败: ${error.message}`, 'error');
      throw error;
    }
  }

  async fillField(selector, value) {
    try {
      const element = document.querySelector(selector);
      if (!element) {
        throw new Error(`找不到元素: ${selector}`);
      }

      // 滚动到元素位置
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });

      // 高亮显示元素
      this.highlightElement(element);

      // 根据元素类型填写值
      switch (element.tagName.toLowerCase()) {
        case 'input':
          await this.fillInput(element, value);
          break;
        case 'select':
          await this.fillSelect(element, value);
          break;
        case 'textarea':
          await this.fillTextarea(element, value);
          break;
        default:
          throw new Error(`不支持的元素类型: ${element.tagName}`);
      }

      // 触发change事件
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('input', { bubbles: true }));

      return true;
    } catch (error) {
      console.error(`填写字段失败:`, error);
      return false;
    }
  }

  async fillInput(element, value) {
    // 清空现有值
    element.value = '';
    element.focus();

    // 模拟用户输入
    for (const char of value.toString()) {
      element.value += char;
      await this.sleep(50); // 模拟打字速度
    }

    element.blur();
  }

  showFillbackOverlay() {
    if (this.fillbackOverlay) return;

    this.fillbackOverlay = document.createElement('div');
    this.fillbackOverlay.className = 'sinoair-fillback-overlay';
    this.fillbackOverlay.innerHTML = `
      <div class="overlay-content">
        <div class="spinner"></div>
        <div class="message">正在执行回填操作...</div>
      </div>
    `;

    document.body.appendChild(this.fillbackOverlay);
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 初始化内容脚本
new ContentScript();
```

#### 3.2.3 弹窗界面 (popup.html)
```html
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Sinoair Agent 回填助手</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <!-- 头部 -->
    <header class="popup-header">
      <div class="logo">
        <img src="../assets/icons/icon-32.png" alt="Sinoair Agent">
        <span>回填助手</span>
      </div>
      <div class="connection-status" id="connectionStatus">
        <span class="status-dot"></span>
        <span class="status-text">未连接</span>
      </div>
    </header>

    <!-- 主内容 -->
    <main class="popup-main">
      <!-- 连接设置 -->
      <div class="section" id="connectionSection">
        <h3>服务器设置</h3>
        <div class="form-group">
          <label>服务器地址:</label>
          <input type="text" id="serverUrl" placeholder="http://localhost:8080">
        </div>
        <div class="form-group">
          <label>用户名:</label>
          <input type="text" id="username" placeholder="请输入用户名">
        </div>
        <div class="form-group">
          <label>密码:</label>
          <input type="password" id="password" placeholder="请输入密码">
        </div>
        <button id="connectBtn" class="btn btn-primary">连接</button>
      </div>

      <!-- 任务列表 -->
      <div class="section" id="taskSection" style="display: none;">
        <div class="section-header">
          <h3>待回填任务</h3>
          <button id="refreshBtn" class="btn btn-small">刷新</button>
        </div>
        <div class="task-list" id="taskList">
          <!-- 任务项将通过JavaScript动态生成 -->
        </div>
      </div>

      <!-- 当前页面信息 -->
      <div class="section" id="pageSection" style="display: none;">
        <h3>当前页面</h3>
        <div class="page-info">
          <div class="info-item">
            <label>URL:</label>
            <span id="currentUrl"></span>
          </div>
          <div class="info-item">
            <label>支持回填:</label>
            <span id="fillbackSupport" class="status-badge">检测中...</span>
          </div>
        </div>
        <div class="page-actions">
          <button id="detectFormBtn" class="btn btn-secondary">检测表单</button>
          <button id="configPageBtn" class="btn btn-secondary">配置页面</button>
        </div>
      </div>
    </main>

    <!-- 底部 -->
    <footer class="popup-footer">
      <div class="footer-links">
        <a href="#" id="settingsLink">设置</a>
        <a href="#" id="helpLink">帮助</a>
      </div>
    </footer>
  </div>

  <script src="popup.js"></script>
</body>
</html>
```

#### 3.2.4 弹窗脚本 (popup.js)
```javascript
// 弹窗界面控制器
class PopupController {
  constructor() {
    this.apiClient = new ApiClient();
    this.init();
  }

  async init() {
    // 绑定事件监听器
    this.bindEvents();

    // 加载保存的配置
    await this.loadConfig();

    // 检查连接状态
    await this.checkConnection();

    // 获取当前页面信息
    await this.getCurrentPageInfo();
  }

  bindEvents() {
    // 连接按钮
    document.getElementById('connectBtn').addEventListener('click', this.handleConnect.bind(this));

    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', this.handleRefresh.bind(this));

    // 检测表单按钮
    document.getElementById('detectFormBtn').addEventListener('click', this.handleDetectForm.bind(this));

    // 设置链接
    document.getElementById('settingsLink').addEventListener('click', this.handleSettings.bind(this));
  }

  async handleConnect() {
    const serverUrl = document.getElementById('serverUrl').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    if (!serverUrl || !username || !password) {
      this.showMessage('请填写完整的连接信息', 'error');
      return;
    }

    try {
      // 显示连接中状态
      this.updateConnectionStatus('connecting', '连接中...');

      // 尝试连接
      const result = await this.apiClient.connect({ serverUrl, username, password });

      if (result.success) {
        // 保存配置
        await this.saveConfig({ serverUrl, username });

        // 更新界面
        this.updateConnectionStatus('connected', '已连接');
        this.showTaskSection();

        // 加载任务列表
        await this.loadTasks();
      } else {
        throw new Error(result.message || '连接失败');
      }
    } catch (error) {
      this.updateConnectionStatus('disconnected', '连接失败');
      this.showMessage(`连接失败: ${error.message}`, 'error');
    }
  }

  async loadTasks() {
    try {
      const tasks = await this.apiClient.getTasks({ status: 'completed' });
      this.renderTaskList(tasks);
    } catch (error) {
      this.showMessage(`加载任务失败: ${error.message}`, 'error');
    }
  }

  renderTaskList(tasks) {
    const taskList = document.getElementById('taskList');

    if (tasks.length === 0) {
      taskList.innerHTML = '<div class="empty-state">暂无待回填任务</div>';
      return;
    }

    taskList.innerHTML = tasks.map(task => `
      <div class="task-item" data-task-id="${task.id}">
        <div class="task-info">
          <div class="task-name">${task.fileName}</div>
          <div class="task-meta">
            <span class="agent-name">${task.agentName}</span>
            <span class="task-time">${this.formatTime(task.createdAt)}</span>
          </div>
        </div>
        <div class="task-actions">
          <button class="btn btn-small btn-primary" onclick="fillbackTask('${task.id}')">
            回填
          </button>
        </div>
      </div>
    `).join('');
  }

  async getCurrentPageInfo() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      document.getElementById('currentUrl').textContent = tab.url;

      // 检测页面是否支持回填
      const support = await this.checkFillbackSupport(tab.url);
      const supportElement = document.getElementById('fillbackSupport');

      if (support) {
        supportElement.textContent = '支持';
        supportElement.className = 'status-badge success';
      } else {
        supportElement.textContent = '不支持';
        supportElement.className = 'status-badge error';
      }

      this.showPageSection();
    } catch (error) {
      console.error('获取页面信息失败:', error);
    }
  }

  updateConnectionStatus(status, text) {
    const statusElement = document.getElementById('connectionStatus');
    const dot = statusElement.querySelector('.status-dot');
    const textElement = statusElement.querySelector('.status-text');

    dot.className = `status-dot ${status}`;
    textElement.textContent = text;
  }

  showTaskSection() {
    document.getElementById('connectionSection').style.display = 'none';
    document.getElementById('taskSection').style.display = 'block';
  }

  showPageSection() {
    document.getElementById('pageSection').style.display = 'block';
  }
}

// 全局函数 - 执行回填任务
async function fillbackTask(taskId) {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    // 发送回填指令到后台脚本
    const result = await chrome.runtime.sendMessage({
      action: 'EXECUTE_FILLBACK',
      data: { taskId, tabId: tab.id }
    });

    if (result.success) {
      showMessage('回填操作已开始', 'success');
    } else {
      throw new Error(result.error);
    }
  } catch (error) {
    showMessage(`回填失败: ${error.message}`, 'error');
  }
}

// 初始化弹窗控制器
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});
```

## 4. 部署和配置

### 4.1 PC客户端打包配置

#### 4.1.1 Electron Builder 配置
```json
{
  "build": {
    "appId": "com.sinoair.agent.fillback",
    "productName": "Sinoair Agent 回填工具",
    "directories": {
      "output": "dist"
    },
    "files": [
      "dist-electron/**/*",
      "dist/**/*",
      "node_modules/**/*"
    ],
    "mac": {
      "category": "public.app-category.productivity",
      "target": [
        {
          "target": "dmg",
          "arch": ["x64", "arm64"]
        }
      ]
    },
    "win": {
      "target": [
        {
          "target": "nsis",
          "arch": ["x64", "ia32"]
        }
      ]
    },
    "linux": {
      "target": [
        {
          "target": "AppImage",
          "arch": ["x64"]
        }
      ]
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true
    }
  }
}
```

#### 4.1.2 自动更新配置
```typescript
// 自动更新服务
class AutoUpdater {
  private updateServer = 'https://updates.sinoair.com';

  async checkForUpdates(): Promise<UpdateInfo | null> {
    try {
      const currentVersion = app.getVersion();
      const response = await fetch(`${this.updateServer}/check-update`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          platform: process.platform,
          arch: process.arch,
          version: currentVersion
        })
      });

      const updateInfo = await response.json();

      if (updateInfo.hasUpdate) {
        return updateInfo;
      }

      return null;
    } catch (error) {
      console.error('检查更新失败:', error);
      return null;
    }
  }

  async downloadAndInstall(updateInfo: UpdateInfo): Promise<void> {
    // 下载更新包
    const downloadPath = await this.downloadUpdate(updateInfo.downloadUrl);

    // 验证文件完整性
    await this.verifyUpdate(downloadPath, updateInfo.checksum);

    // 安装更新
    await this.installUpdate(downloadPath);
  }
}
```

### 4.2 浏览器插件打包配置

#### 4.2.1 构建脚本
```json
{
  "scripts": {
    "build:extension": "webpack --config webpack.extension.js --mode production",
    "dev:extension": "webpack --config webpack.extension.js --mode development --watch",
    "package:extension": "npm run build:extension && web-ext build --source-dir=dist/extension",
    "test:extension": "web-ext lint --source-dir=dist/extension"
  }
}
```

#### 4.2.2 Webpack 配置
```javascript
// webpack.extension.js
const path = require('path');
const CopyPlugin = require('copy-webpack-plugin');

module.exports = {
  entry: {
    background: './src/extension/background.js',
    content: './src/extension/content.js',
    popup: './src/extension/popup/popup.js'
  },
  output: {
    path: path.resolve(__dirname, 'dist/extension'),
    filename: '[name].js'
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env']
          }
        }
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      }
    ]
  },
  plugins: [
    new CopyPlugin({
      patterns: [
        { from: 'src/extension/manifest.json', to: 'manifest.json' },
        { from: 'src/extension/popup/popup.html', to: 'popup.html' },
        { from: 'src/extension/popup/popup.css', to: 'popup.css' },
        { from: 'src/extension/assets', to: 'assets' }
      ]
    })
  ]
};
```

## 5. 安全和权限管理

### 5.1 数据安全
- **敏感信息加密**: 用户凭据和API密钥使用AES加密存储
- **HTTPS通信**: 所有API通信强制使用HTTPS
- **数据脱敏**: 日志中自动脱敏敏感信息
- **本地存储安全**: 使用系统密钥库存储敏感配置

### 5.2 权限控制
- **最小权限原则**: 插件只请求必要的权限
- **用户授权**: 敏感操作需要用户明确授权
- **域名白名单**: 限制插件只在授权域名下工作
- **操作审计**: 记录所有回填操作的详细日志

### 5.3 错误处理和恢复
- **网络异常**: 自动重试机制和离线模式
- **回填失败**: 详细错误信息和恢复建议
- **数据备份**: 重要配置和数据的自动备份
- **回滚机制**: 支持回滚到上一个稳定版本

## 6. 监控和分析

### 6.1 使用统计
- **功能使用频率**: 统计各功能的使用情况
- **成功率监控**: 监控回填操作的成功率
- **性能指标**: 记录响应时间和资源使用情况
- **错误统计**: 分析常见错误和失败原因

### 6.2 用户反馈
- **错误报告**: 自动收集崩溃和错误信息
- **用户评价**: 收集用户对回填准确性的评价
- **改进建议**: 提供反馈渠道收集改进建议
- **使用体验**: 监控用户操作流程和体验

## 7. 总结

### 7.1 设计特点
1. **双端支持**: PC客户端和浏览器插件两种方式，满足不同使用场景
2. **实时同步**: 通过WebSocket实现实时任务状态同步
3. **智能回填**: 基于配置的智能字段映射和自动填写
4. **安全可靠**: 完善的安全机制和错误处理
5. **易于扩展**: 模块化设计，便于功能扩展和维护

### 7.2 技术优势
- **跨平台兼容**: 支持Windows、macOS、Linux多平台
- **现代化技术栈**: 使用最新的前端技术和开发工具
- **高性能**: 优化的代码结构和资源使用
- **用户友好**: 直观的界面设计和操作流程

### 7.3 应用场景
- **航空货运代理**: 自动填写运单、发票等单证信息
- **物流管理**: 批量处理货运文档和系统录入
- **数据迁移**: 从旧系统向新系统迁移数据
- **重复性工作**: 减少人工录入，提高工作效率

这套回填工具设计方案提供了完整的技术实现路径，能够有效支持Sinoair Agent系统的自动化回填需求，大幅提升用户的工作效率和准确性。