# Sinoair Agent 产品需求文档 (PRD)

## 1. 产品概述

### 1.1 产品名称
Sinoair Agent - 国际航空货运代理智能识别与回填系统

### 1.2 产品背景
在国际航空货运代理行业中，业务人员需要处理大量的单证文件（图片、PDF等格式），这些文件虽然格式不同，但内容都遵循IATA（国际航空运输协会）规范。传统的人工录入方式效率低下且容易出错，急需智能化解决方案。

### 1.3 产品定位
基于LLM大模型的智能文档识别与自动化回填平台，专为国际航空货运代理行业设计，通过AI技术实现单证自动识别、数据提取和系统回填，大幅提升工作效率和准确性。

### 1.4 产品价值
- **效率提升**: 自动识别和回填，减少90%的人工录入时间
- **准确性保障**: AI识别准确率达95%以上，减少人为错误
- **成本降低**: 减少人力成本，提高业务处理能力
- **标准化**: 统一的IATA规范数据格式，确保数据一致性

## 2. 目标用户

### 2.1 主要用户群体

#### 2.1.1 航空代理员工
- **用户特征**: 日常处理大量航空货运单证的业务人员
- **核心需求**: 快速、准确地识别和录入单证信息
- **使用场景**: 处理运单、发票、装箱单等各类单证
- **痛点**: 手工录入效率低、容易出错、重复性工作多

#### 2.1.2 系统管理员
- **用户特征**: 负责系统维护和管理的技术人员
- **核心需求**: 系统配置、用户管理、性能监控
- **使用场景**: 系统部署、配置管理、故障处理
- **痛点**: 系统复杂度高、维护工作量大

### 2.2 用户画像

**典型用户A - 货运操作员**
- 年龄: 25-35岁
- 工作经验: 3-8年航空货运经验
- 技能水平: 熟悉航空货运流程，计算机操作熟练
- 工作环境: 办公室环境，多系统操作
- 核心诉求: 提高工作效率，减少重复性工作

**典型用户B - 系统管理员**
- 年龄: 28-40岁
- 技术背景: 计算机相关专业，5年以上系统管理经验
- 技能水平: 精通系统配置和维护
- 工作环境: 数据中心或远程管理
- 核心诉求: 系统稳定运行，便于维护管理

## 3. 产品功能

### 3.1 功能架构

```mermaid
graph LR
    A[Sinoair Agent 系统] --> B[系统管理]
    A --> C[Agent管理]
    A --> D[Agent使用]

    B --> B1[用户管理]
    B --> B2[大模型管理]
    B --> B3[回填网站管理]
    B --> B4[日志查看]

    C --> C1[Agent创建和维护]
    C --> C2[Prompt调试]
    C --> C3[回填网站绑定]
    C --> C4[历史记录查看]

    D --> D1[Web界面使用]
    D --> D2[浏览器插件]
    D --> D3[PC客户端工具]
```

### 3.2 核心功能详述

#### 3.2.1 系统管理模块

**用户管理**
- 功能描述: 管理系统用户账户和权限
- 主要功能:
  - 用户注册、登录、信息管理
  - 角色权限配置（系统管理员、普通用户）
  - 用户状态管理（启用、禁用、锁定）
  - 密码策略和安全设置

**大模型管理**
- 功能描述: 管理和配置LLM大模型
- 主要功能:
  - 支持多种LLM提供商（OpenAI、Anthropic、百度、阿里等）
  - 模型配置管理（API密钥、参数调优）
  - 模型性能监控和使用统计
  - 模型状态管理（启用、禁用）

**回填网站管理**
- 功能描述: 管理目标回填网站配置
- 主要功能:
  - 网站基本信息配置（URL、名称、描述）
  - 登录认证配置（用户名密码、Token等）
  - 页面元素定位配置
  - 网站访问测试和验证

**日志查看**
- 功能描述: 系统操作日志查看和分析
- 主要功能:
  - 操作日志记录和查询
  - 系统性能监控
  - 错误日志分析
  - 用户行为统计

#### 3.2.2 Agent管理模块

**Agent创建和维护**
- 功能描述: Agent的全生命周期管理
- 主要功能:
  - Agent基本信息配置
  - 系统提示词和用户提示词模板编写
  - 输出JSON格式定义
  - Agent分类管理（按单证类型）
  - Agent状态管理（草稿、测试、发布、归档）

**Prompt调试**
- 功能描述: Agent提示词的编辑、测试和优化
- 主要功能:
  - 在线提示词编辑器（支持语法高亮）
  - 测试文件上传和预览
  - 实时调试和结果预览
  - 提示词版本对比
  - 性能指标监控（准确率、响应时间）

**回填网站绑定**
- 功能描述: 配置Agent与目标网站的字段映射
- 主要功能:
  - 字段映射配置
  - 数据转换规则设置
  - 回填脚本配置
  - 映射关系测试

**历史记录查看**
- 功能描述: Agent使用历史和结果追踪
- 主要功能:
  - 处理历史记录查看
  - 输入文件和输出结果对比
  - 错误记录和分析
  - 处理结果评价和反馈
  - 数据导出功能

#### 3.2.3 Agent使用模块

**Web界面使用**
- 功能描述: 基于Web的Agent使用界面
- 主要功能:
  - 文件上传（支持图片、PDF等格式）
  - Agent选择和配置
  - 实时处理进度显示
  - 结果预览和编辑
  - 一键回写功能

**浏览器插件**
- 功能描述: 浏览器插件形式的Agent使用工具
- 主要功能:
  - 插件安装和配置
  - 页面内文件识别
  - 右键菜单快速调用
  - 结果直接回填到当前页面

**PC客户端工具**
- 功能描述: 独立的桌面应用程序
- 主要功能:
  - 任务监控和管理
  - 批量文件处理
  - 自动化回填执行
  - 离线模式支持

## 4. 业务流程

### 4.1 Agent创建流程

```mermaid
flowchart TD
    A[开始创建Agent] --> B[填写基本信息]
    B --> C[选择单证类型]
    C --> D[编写系统提示词]
    D --> E[定义输出格式]
    E --> F[上传测试样本]
    F --> G[调试测试]
    G --> H{测试通过?}
    H -->|否| I[修改提示词]
    I --> G
    H -->|是| J[保存Agent]
    J --> K[发布Agent]
    K --> L[结束]
```

### 4.2 文件处理业务流程

```mermaid
flowchart TD
    A[用户上传文件] --> B[文件格式验证]
    B --> C{格式正确?}
    C -->|否| D[返回错误信息]
    C -->|是| E[文件预处理]
    E --> F[选择对应Agent]
    F --> G[调用LLM识别]
    G --> H[结果验证]
    H --> I{验证通过?}
    I -->|否| J[人工校验]
    I -->|是| K[格式化输出]
    J --> K
    K --> L[保存结果]
    L --> M[用户确认]
    M --> N{需要回写?}
    N -->|是| O[执行回写]
    N -->|否| P[完成处理]
    O --> P
```

### 4.3 回写操作流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant W as Web界面
    participant R as 回写服务
    participant T as 目标网站
    
    U->>W: 确认回写
    W->>R: 发送回写请求
    R->>T: 自动化登录
    R->>T: 定位页面元素
    R->>T: 填写数据
    R->>W: 返回回写结果
    W->>U: 显示回写状态
```

## 5. 技术架构

### 5.1 技术栈选择

**后端技术栈**
- Java 17 + Spring Boot 3.5.0
- MySQL 8.x + Redis + MongoDB
- MyBatis-Plus 3.5.5
- Spring Security 6.x
- Puppeteer/Playwright (自动化)

**前端技术栈**
- Vue 3.5.16 + TypeScript 5.8.3
- Element Plus 2.9.11
- Pinia (状态管理)
- Vite 6.3.5 (构建工具)

**回填工具**
- PC客户端: Electron + Vue 3 + TypeScript
- 浏览器插件: Manifest V3 + Vue 3

### 5.2 系统架构

采用分层架构设计，包括：
- 表现层：Web界面、API接口
- 业务层：业务逻辑、事务管理
- 持久层：数据访问、SQL映射
- 数据层：MySQL、Redis、MongoDB

### 5.3 部署架构

支持云原生部署：
- 容器化部署（Docker）
- 微服务架构
- 负载均衡和高可用
- 自动扩缩容

## 6. 数据模型

### 6.1 核心数据实体

**用户管理**
- 用户表 (users)
- 角色表 (roles)
- 权限表 (permissions)
- 用户角色关联表 (user_roles)

**Agent管理**
- Agent表 (agents)
- Agent版本表 (agent_versions)
- LLM模型表 (llm_models)
- Agent测试记录表 (agent_tests)

**文件处理**
- 处理记录表 (processing_records)
- 处理批次表 (processing_batches)
- 处理日志表 (processing_logs)

**网站回填**
- 网站配置表 (websites)
- 回填记录表 (fillback_records)
- 字段映射表 (field_mappings)

### 6.2 数据关系

总计28张表，核心业务表15张，支持完整的业务流程和数据管理。

## 7. 用户体验设计

### 7.1 界面设计原则
- **简洁直观**: 界面布局清晰，操作流程简单
- **响应式设计**: 支持多种屏幕尺寸和设备
- **一致性**: 统一的视觉风格和交互模式
- **可访问性**: 支持键盘导航和屏幕阅读器

### 7.2 关键用户界面

**主控制台**
- 任务概览仪表板
- 快速操作入口
- 系统状态监控
- 最近处理记录

**Agent管理界面**
- Agent列表和分类
- 创建/编辑向导
- 调试测试工具
- 性能统计图表

**文件处理界面**
- 拖拽上传区域
- 处理进度显示
- 结果预览和编辑
- 回填操作面板

### 7.3 交互设计

**文件上传**
- 支持拖拽上传
- 批量文件选择
- 上传进度显示
- 文件格式验证

**结果展示**
- 结构化数据表格
- 字段高亮显示
- 置信度指示器
- 编辑模式切换

**回填操作**
- 网站选择下拉
- 字段映射预览
- 一键回填按钮
- 执行状态反馈

## 8. 性能要求

### 8.1 响应时间要求
- **页面加载**: 首页加载时间 < 3秒
- **文件上传**: 支持最大50MB文件，上传响应 < 5秒
- **LLM处理**: 单个文件处理时间 < 30秒
- **回填操作**: 单次回填操作 < 10秒

### 8.2 并发性能
- **用户并发**: 支持100个并发用户
- **文件处理**: 支持10个并发处理任务
- **API调用**: 支持1000 QPS
- **数据库**: 支持10000个并发连接

### 8.3 可用性要求
- **系统可用性**: 99.9%
- **数据可靠性**: 99.99%
- **故障恢复**: RTO < 1小时，RPO < 15分钟
- **备份策略**: 每日全量备份，每小时增量备份

## 9. 安全要求

### 9.1 身份认证
- **多因素认证**: 支持密码+短信验证码
- **单点登录**: 支持企业SSO集成
- **会话管理**: 自动超时和强制登出
- **密码策略**: 复杂度要求和定期更换

### 9.2 数据安全
- **数据加密**: 敏感数据AES-256加密存储
- **传输安全**: 全站HTTPS加密传输
- **访问控制**: 基于角色的权限控制
- **数据脱敏**: 日志中自动脱敏敏感信息

### 9.3 系统安全
- **API安全**: 接口访问频率限制
- **输入验证**: 防止SQL注入和XSS攻击
- **安全审计**: 完整的操作日志记录
- **漏洞扫描**: 定期安全漏洞检测

## 10. 运维要求

### 10.1 监控告警
- **系统监控**: CPU、内存、磁盘、网络
- **应用监控**: API响应时间、错误率、吞吐量
- **业务监控**: 处理成功率、用户活跃度
- **告警机制**: 邮件、短信、企业微信通知

### 10.2 日志管理
- **日志分类**: 系统日志、应用日志、业务日志
- **日志存储**: 集中化日志存储和检索
- **日志分析**: 实时日志分析和报表
- **日志保留**: 按重要性分级保留策略

### 10.3 部署运维
- **自动化部署**: CI/CD流水线
- **版本管理**: 灰度发布和快速回滚
- **配置管理**: 环境配置分离和动态更新
- **容量规划**: 自动扩缩容和资源优化

## 11. 项目规划

### 11.1 开发阶段

**第一阶段 (MVP版本) - 3个月**
- 基础用户管理和认证
- 核心Agent管理功能
- 基本文件处理和LLM集成
- 简单的Web界面

**第二阶段 (完整版本) - 2个月**
- 完整的回填功能
- 浏览器插件开发
- 系统管理和监控
- 性能优化和安全加固

**第三阶段 (增强版本) - 2个月**
- PC客户端工具
- 高级分析和报表
- 企业级功能集成
- 移动端支持

### 11.2 里程碑计划

| 里程碑 | 时间节点 | 主要交付物 |
|--------|----------|------------|
| M1 | 第1个月 | 基础架构和用户管理 |
| M2 | 第2个月 | Agent管理和LLM集成 |
| M3 | 第3个月 | 文件处理和Web界面 |
| M4 | 第4个月 | 回填功能和浏览器插件 |
| M5 | 第5个月 | 系统管理和监控 |
| M6 | 第6个月 | PC客户端和企业功能 |
| M7 | 第7个月 | 性能优化和测试 |

### 11.3 资源需求

**开发团队**
- 项目经理: 1人
- 后端开发: 3人
- 前端开发: 2人
- 测试工程师: 2人
- 运维工程师: 1人

**技术资源**
- 开发环境: 云服务器、开发工具
- 测试环境: 性能测试工具、自动化测试
- 生产环境: 高可用集群、监控系统

## 12. 风险评估

### 12.1 技术风险
- **LLM API稳定性**: 第三方API服务中断风险
- **性能瓶颈**: 大文件处理和高并发场景
- **兼容性问题**: 不同浏览器和操作系统
- **数据安全**: 敏感信息泄露风险

### 12.2 业务风险
- **用户接受度**: 新技术学习成本
- **准确性要求**: AI识别准确率期望
- **集成复杂度**: 与现有系统集成难度
- **合规要求**: 行业法规和标准符合性

### 12.3 风险应对

**技术风险应对**
- 多LLM提供商备选方案
- 性能测试和优化策略
- 跨平台兼容性测试
- 安全审计和渗透测试

**业务风险应对**
- 用户培训和技术支持
- 人工校验和反馈机制
- 分阶段集成和试点推广
- 合规性评估和认证

## 13. 成功指标

### 13.1 业务指标
- **效率提升**: 文档处理时间减少80%以上
- **准确性**: AI识别准确率达到95%以上
- **用户满意度**: 用户满意度评分 > 4.5/5.0
- **成本节约**: 人力成本降低60%以上

### 13.2 技术指标
- **系统可用性**: 99.9%以上
- **响应时间**: 平均响应时间 < 2秒
- **错误率**: 系统错误率 < 0.1%
- **并发处理**: 支持100并发用户

### 13.3 用户指标
- **活跃用户**: 月活跃用户 > 500人
- **使用频率**: 日均处理文档 > 1000份
- **功能使用**: 核心功能使用率 > 80%
- **用户留存**: 月用户留存率 > 90%

## 14. 总结

Sinoair Agent作为专为国际航空货运代理行业设计的智能识别与回填系统，通过LLM技术和自动化工具的结合，将显著提升行业的数字化水平和工作效率。

**产品核心价值**:
1. **智能化**: 基于先进的LLM技术，实现文档智能识别
2. **自动化**: 端到端的自动化流程，减少人工干预
3. **标准化**: 符合IATA规范，确保数据一致性
4. **易用性**: 直观的用户界面，降低学习成本
5. **可扩展**: 模块化设计，支持功能扩展和定制

**实施建议**:
- 采用敏捷开发方法，快速迭代和反馈
- 重视用户体验设计，确保产品易用性
- 建立完善的测试体系，保证产品质量
- 制定详细的运维计划，确保系统稳定性

通过本产品的成功实施，将为国际航空货运代理行业带来革命性的效率提升，推动行业数字化转型的进程。
