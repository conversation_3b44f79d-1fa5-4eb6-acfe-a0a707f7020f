# Sinoair Agent 数据库设计文档

## 1. 数据库概述

### 1.1 设计原则

- **规范化设计**：遵循第三范式，减少数据冗余
- **性能优化**：合理设置索引，优化查询性能
- **扩展性**：支持水平扩展和垂直扩展
- **安全性**：敏感数据加密存储，访问权限控制
- **一致性**：保证数据的完整性和一致性

### 1.2 数据库架构

- **主数据库**：MySQL 8.0 - 存储核心业务数据
- **文档数据库**：MongoDB - 存储非结构化数据（日志、配置等）
- **缓存数据库**：Redis - 缓存热点数据，会话管理
- **搜索引擎**：Elasticsearch - 全文搜索，日志分析

### 1.3 数据库命名规范

- **数据库名**：sinoair_agent
- **表名**：小写字母，下划线分隔，复数形式
- **字段名**：小写字母，下划线分隔
- **索引名**：idx_表名_字段名

## 2. 数据库表结构关系图

### 2.1 核心表关系图

```mermaid
erDiagram
    USERS ||--o{ USER_ROLES : has
    ROLES ||--o{ USER_ROLES : assigned
    ROLES ||--o{ ROLE_PERMISSIONS : has
    PERMISSIONS ||--o{ ROLE_PERMISSIONS : granted

    USERS ||--o{ AGENTS : creates
    USERS ||--o{ PROCESSING_RECORDS : processes
    USERS ||--o{ OPERATION_LOGS : generates

    AGENTS ||--o{ AGENT_VERSIONS : has
    AGENTS ||--o{ PROCESSING_RECORDS : uses
    AGENTS ||--o{ AGENT_TESTS : tested_by
    AGENTS ||--o{ FIELD_MAPPINGS : maps

    PROCESSING_RECORDS ||--o{ FILLBACK_RECORDS : generates
    PROCESSING_RECORDS ||--o{ PROCESSING_LOGS : logs

    WEBSITES ||--o{ FILLBACK_RECORDS : targets
    WEBSITES ||--o{ FIELD_MAPPINGS : maps
    WEBSITES ||--o{ WEBSITE_CONFIGS : configures

    LLM_MODELS ||--o{ AGENTS : uses

    DEPARTMENTS ||--o{ USERS : contains

    USERS {
        bigint id
        varchar username
        varchar email
        varchar password_hash
        varchar real_name
        varchar phone
        bigint department_id FK
        varchar status
        datetime created_at
        datetime updated_at
        datetime last_login_at
    }

    ROLES {
        bigint id
        varchar name 
        varchar code
        varchar description
        varchar status
        datetime created_at
        datetime updated_at
    }

    PERMISSIONS {
        bigint id
        varchar name
        varchar code
        varchar resource
        varchar action
        varchar description
        datetime created_at 
    }

    AGENTS {
        bigint id
        varchar name
        text description
        varchar category
        varchar version
        varchar status
        bigint creator_id
        bigint llm_model_id
        text system_prompt
        text user_prompt_template
        json output_schema
        json validation_rules
        json config
        datetime created_at
        datetime updated_at
        datetime published_at
    }

    PROCESSING_RECORDS {
        bigint id
        bigint user_id
        bigint agent_id
        varchar file_name
        varchar file_path
        bigint file_size
        varchar file_type
        varchar file_hash
        varchar status
        json input_data
        json output_data
        text error_message
        int processing_time
        decimal confidence_score
        datetime created_at
        datetime completed_at
    }

    WEBSITES {
        bigint id
        varchar name
        varchar url
        text description
        varchar auth_type
        json auth_config
        json selectors
        varchar status
        bigint creator_id FK
        datetime created_at
        datetime updated_at
    }

    FILLBACK_RECORDS {
        bigint id
        bigint processing_record_id
        bigint website_id
        varchar status
        json fillback_data
        text error_message
        int retry_count
        int execution_time
        varchar browser_type
        datetime created_at
        datetime completed_at
    }
```

### 2.2 扩展表关系图

```mermaid
erDiagram
    AGENT_CATEGORIES ||--o{ AGENTS : categorizes
    AGENT_TAGS ||--o{ AGENT_TAG_RELATIONS : has
    AGENTS ||--o{ AGENT_TAG_RELATIONS : tagged

    PROCESSING_BATCHES ||--o{ PROCESSING_RECORDS : contains

    NOTIFICATIONS ||--o{ USER_NOTIFICATIONS : sends
    USERS ||--o{ USER_NOTIFICATIONS : receives

    FILE_TEMPLATES ||--o{ AGENTS : uses

    AGENT_CATEGORIES {
        bigint id
        varchar name
        varchar code
        text description
        varchar icon
        int sort_order
        varchar status
        datetime created_at
    }

    AGENT_TAGS {
        bigint id
        varchar name
        varchar color
        text description
        datetime created_at
    }

    PROCESSING_BATCHES {
        bigint id
        varchar batch_name
        bigint user_id
        bigint agent_id
        int total_files
        int processed_files
        int success_files
        int failed_files
        varchar status
        datetime created_at
        datetime completed_at
    }

    SYSTEM_CONFIGS {
        bigint id
        varchar config_key
        varchar config_value
        varchar config_type
        text description
        varchar status
        datetime created_at
        datetime updated_at
    }

    NOTIFICATIONS {
        bigint id
        varchar title
        text content
        varchar type
        varchar level
        varchar target_type
        text target_ids
        bigint sender_id
        datetime send_time
        datetime expire_time
        varchar status
        int read_count
        int total_count
        datetime created_at
    }

    USER_NOTIFICATIONS {
        bigint id
        bigint notification_id
        bigint user_id
        boolean is_read
        datetime read_time
        datetime created_at
    }

    FILE_TEMPLATES {
        bigint id
        varchar name
        varchar category
        varchar file_type
        varchar template_path
        varchar preview_image
        text description
        json field_mapping
        json validation_rules
        int usage_count
        varchar status
        bigint created_by
        datetime created_at
        datetime updated_at
    }
```

## 3. 核心表结构详细设计

### 3.1 用户管理相关表

#### 3.1.1 用户表 (users)

```sql
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱地址',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希值',
    real_name VARCHAR(100) COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号码',
    avatar VARCHAR(500) COMMENT '头像URL',
    department_id BIGINT COMMENT '部门ID',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-激活，inactive-禁用，locked-锁定',
    last_login_at DATETIME COMMENT '最后登录时间',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_users_username (username),
    INDEX idx_users_email (email),
    INDEX idx_users_department (department_id),
    INDEX idx_users_status (status),
    INDEX idx_users_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

#### 3.1.2 角色表 (roles)

```sql
CREATE TABLE roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '角色ID',
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '角色名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description TEXT COMMENT '角色描述',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-激活，inactive-禁用',
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_roles_code (code),
    INDEX idx_roles_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';
```

#### 3.1.3 权限表 (permissions)

```sql
CREATE TABLE permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '权限ID',
    name VARCHAR(100) NOT NULL COMMENT '权限名称',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    resource VARCHAR(100) COMMENT '资源标识',
    action VARCHAR(50) COMMENT '操作类型：create,read,update,delete',
    description TEXT COMMENT '权限描述',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_permissions_code (code),
    INDEX idx_permissions_resource (resource)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';
```

#### 3.1.4 用户角色关联表 (user_roles)

```sql
CREATE TABLE user_roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_user_roles_user (user_id),
    INDEX idx_user_roles_role (role_id),

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';
```

#### 3.1.5 角色权限关联表 (role_permissions)

```sql
CREATE TABLE role_permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    UNIQUE KEY uk_role_permission (role_id, permission_id),
    INDEX idx_role_permissions_role (role_id),
    INDEX idx_role_permissions_permission (permission_id),

    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';
```

#### 3.1.6 部门表 (departments)

```sql
CREATE TABLE departments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '部门ID',
    name VARCHAR(100) NOT NULL COMMENT '部门名称',
    code VARCHAR(50) UNIQUE COMMENT '部门编码',
    parent_id BIGINT COMMENT '上级部门ID',
    level INT DEFAULT 1 COMMENT '部门层级',
    path VARCHAR(500) COMMENT '部门路径',
    manager_id BIGINT COMMENT '部门负责人ID',
    description TEXT COMMENT '部门描述',
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-激活，inactive-禁用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_departments_parent (parent_id),
    INDEX idx_departments_code (code),
    INDEX idx_departments_status (status),

    FOREIGN KEY (parent_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';
```

### 3.2 Agent管理相关表

#### 3.2.1 LLM模型表 (llm_models)

```sql
CREATE TABLE llm_models (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '模型ID',
    name VARCHAR(100) NOT NULL COMMENT '模型名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '模型编码',
    provider VARCHAR(50) NOT NULL COMMENT '提供商：openai,anthropic,baidu,alibaba',
    model_version VARCHAR(50) COMMENT '模型版本',
    api_endpoint VARCHAR(500) COMMENT 'API端点',
    max_tokens INT DEFAULT 4096 COMMENT '最大Token数',
    temperature DECIMAL(3,2) DEFAULT 0.7 COMMENT '温度参数',
    top_p DECIMAL(3,2) DEFAULT 1.0 COMMENT 'Top-p参数',
    supports_vision BOOLEAN DEFAULT FALSE COMMENT '是否支持视觉识别',
    supports_function_call BOOLEAN DEFAULT FALSE COMMENT '是否支持函数调用',
    cost_per_1k_tokens DECIMAL(10,6) COMMENT '每1K Token成本',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-激活，inactive-禁用',
    description TEXT COMMENT '模型描述',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_llm_models_code (code),
    INDEX idx_llm_models_provider (provider),
    INDEX idx_llm_models_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='LLM模型表';
```

#### 3.2.2 Agent分类表 (agent_categories)

```sql
CREATE TABLE agent_categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID',
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '分类名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '分类编码',
    description TEXT COMMENT '分类描述',
    icon VARCHAR(100) COMMENT '图标',
    color VARCHAR(20) COMMENT '颜色',
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-激活，inactive-禁用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_agent_categories_code (code),
    INDEX idx_agent_categories_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Agent分类表';
```

#### 3.2.3 Agent表 (agents)

```sql
CREATE TABLE agents (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'Agent ID',
    name VARCHAR(200) NOT NULL COMMENT 'Agent名称',
    description TEXT COMMENT 'Agent描述',
    category_id BIGINT COMMENT '分类ID',
    version VARCHAR(20) DEFAULT '1.0.0' COMMENT '版本号',
    status VARCHAR(20) DEFAULT 'draft' COMMENT '状态：draft-草稿，testing-测试中，published-已发布，archived-已归档',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    llm_model_id BIGINT NOT NULL COMMENT 'LLM模型ID',
    system_prompt TEXT NOT NULL COMMENT '系统提示词',
    user_prompt_template TEXT NOT NULL COMMENT '用户提示词模板',
    output_schema JSON NOT NULL COMMENT '输出格式定义',
    validation_rules JSON COMMENT '验证规则',
    config JSON COMMENT 'Agent配置',
    tags VARCHAR(500) COMMENT '标签，逗号分隔',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    success_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '成功率',
    avg_processing_time INT DEFAULT 0 COMMENT '平均处理时间(毫秒)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    published_at DATETIME COMMENT '发布时间',

    INDEX idx_agents_name (name),
    INDEX idx_agents_category (category_id),
    INDEX idx_agents_status (status),
    INDEX idx_agents_creator (creator_id),
    INDEX idx_agents_model (llm_model_id),
    INDEX idx_agents_created (created_at),

    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (category_id) REFERENCES agent_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (llm_model_id) REFERENCES llm_models(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Agent表';
```

#### 3.2.4 Agent版本表 (agent_versions)

```sql
CREATE TABLE agent_versions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '版本ID',
    agent_id BIGINT NOT NULL COMMENT 'Agent ID',
    version VARCHAR(20) NOT NULL COMMENT '版本号',
    system_prompt TEXT NOT NULL COMMENT '系统提示词',
    user_prompt_template TEXT NOT NULL COMMENT '用户提示词模板',
    output_schema JSON NOT NULL COMMENT '输出格式定义',
    validation_rules JSON COMMENT '验证规则',
    config JSON COMMENT 'Agent配置',
    change_log TEXT COMMENT '变更日志',
    performance_metrics JSON COMMENT '性能指标',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    UNIQUE KEY uk_agent_version (agent_id, version),
    INDEX idx_agent_versions_agent (agent_id),
    INDEX idx_agent_versions_created (created_at),

    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Agent版本表';
```

#### 3.2.5 Agent测试记录表 (agent_tests)

```sql
CREATE TABLE agent_tests (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '测试ID',
    agent_id BIGINT NOT NULL COMMENT 'Agent ID',
    test_name VARCHAR(200) COMMENT '测试名称',
    test_file_path VARCHAR(500) NOT NULL COMMENT '测试文件路径',
    test_file_name VARCHAR(255) NOT NULL COMMENT '测试文件名',
    expected_output JSON COMMENT '期望输出',
    actual_output JSON COMMENT '实际输出',
    test_result VARCHAR(20) NOT NULL COMMENT '测试结果：passed-通过，failed-失败',
    accuracy_score DECIMAL(5,2) COMMENT '准确率分数',
    processing_time INT COMMENT '处理时间(毫秒)',
    error_message TEXT COMMENT '错误信息',
    test_by BIGINT NOT NULL COMMENT '测试人员ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_agent_tests_agent (agent_id),
    INDEX idx_agent_tests_result (test_result),
    INDEX idx_agent_tests_created (created_at),

    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (test_by) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Agent测试记录表';
```

#### 3.2.6 Agent标签表 (agent_tags)

```sql
CREATE TABLE agent_tags (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID',
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '标签名称',
    color VARCHAR(20) DEFAULT '#1890ff' COMMENT '标签颜色',
    description TEXT COMMENT '标签描述',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_agent_tags_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Agent标签表';
```

#### 3.2.7 Agent标签关联表 (agent_tag_relations)

```sql
CREATE TABLE agent_tag_relations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
    agent_id BIGINT NOT NULL COMMENT 'Agent ID',
    tag_id BIGINT NOT NULL COMMENT '标签ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    UNIQUE KEY uk_agent_tag (agent_id, tag_id),
    INDEX idx_agent_tag_relations_agent (agent_id),
    INDEX idx_agent_tag_relations_tag (tag_id),

    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES agent_tags(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Agent标签关联表';
```

### 3.3 文件处理相关表

#### 3.3.1 处理批次表 (processing_batches)

```sql
CREATE TABLE processing_batches (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '批次ID',
    batch_name VARCHAR(200) NOT NULL COMMENT '批次名称',
    batch_code VARCHAR(50) UNIQUE COMMENT '批次编码',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    agent_id BIGINT NOT NULL COMMENT 'Agent ID',
    total_files INT DEFAULT 0 COMMENT '总文件数',
    processed_files INT DEFAULT 0 COMMENT '已处理文件数',
    success_files INT DEFAULT 0 COMMENT '成功文件数',
    failed_files INT DEFAULT 0 COMMENT '失败文件数',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态：pending-待处理，processing-处理中，completed-已完成，failed-失败',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '进度百分比',
    estimated_time INT COMMENT '预估时间(秒)',
    actual_time INT COMMENT '实际耗时(秒)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    started_at DATETIME COMMENT '开始时间',
    completed_at DATETIME COMMENT '完成时间',

    INDEX idx_processing_batches_user (user_id),
    INDEX idx_processing_batches_agent (agent_id),
    INDEX idx_processing_batches_status (status),
    INDEX idx_processing_batches_created (created_at),

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='处理批次表';
```

#### 3.3.2 文件处理记录表 (processing_records)

```sql
CREATE TABLE processing_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '处理记录ID',
    batch_id BIGINT COMMENT '批次ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    agent_id BIGINT NOT NULL COMMENT 'Agent ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(50) NOT NULL COMMENT '文件类型：pdf,jpg,png,tiff等',
    file_hash VARCHAR(64) COMMENT '文件哈希值',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态：pending-待处理，processing-处理中，completed-已完成，failed-失败',
    input_data JSON COMMENT '输入数据',
    output_data JSON COMMENT '输出数据',
    raw_response TEXT COMMENT 'LLM原始响应',
    error_message TEXT COMMENT '错误信息',
    error_code VARCHAR(50) COMMENT '错误代码',
    processing_time INT COMMENT '处理时间(毫秒)',
    confidence_score DECIMAL(5,2) COMMENT '置信度分数',
    token_usage JSON COMMENT 'Token使用情况',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    started_at DATETIME COMMENT '开始处理时间',
    completed_at DATETIME COMMENT '完成时间',

    INDEX idx_processing_records_batch (batch_id),
    INDEX idx_processing_records_user (user_id),
    INDEX idx_processing_records_agent (agent_id),
    INDEX idx_processing_records_status (status),
    INDEX idx_processing_records_created (created_at),
    INDEX idx_processing_records_file_hash (file_hash),

    FOREIGN KEY (batch_id) REFERENCES processing_batches(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件处理记录表';
```

#### 3.3.3 处理日志表 (processing_logs)

```sql
CREATE TABLE processing_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    processing_record_id BIGINT NOT NULL COMMENT '处理记录ID',
    step_name VARCHAR(100) NOT NULL COMMENT '步骤名称',
    step_order INT NOT NULL COMMENT '步骤顺序',
    status VARCHAR(20) NOT NULL COMMENT '状态：started-开始，completed-完成，failed-失败',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    duration INT COMMENT '耗时(毫秒)',
    input_data JSON COMMENT '输入数据',
    output_data JSON COMMENT '输出数据',
    error_message TEXT COMMENT '错误信息',
    metadata JSON COMMENT '元数据',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_processing_logs_record (processing_record_id),
    INDEX idx_processing_logs_step (step_name),
    INDEX idx_processing_logs_status (status),
    INDEX idx_processing_logs_created (created_at),

    FOREIGN KEY (processing_record_id) REFERENCES processing_records(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='处理日志表';
```

#### 3.3.4 文件模板表 (file_templates)

```sql
CREATE TABLE file_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '模板ID',
    name VARCHAR(200) NOT NULL COMMENT '模板名称',
    category VARCHAR(100) COMMENT '模板分类',
    file_type VARCHAR(50) NOT NULL COMMENT '文件类型',
    template_path VARCHAR(500) COMMENT '模板文件路径',
    preview_image VARCHAR(500) COMMENT '预览图片路径',
    description TEXT COMMENT '模板描述',
    field_mapping JSON COMMENT '字段映射配置',
    validation_rules JSON COMMENT '验证规则',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-激活，inactive-禁用',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_file_templates_category (category),
    INDEX idx_file_templates_type (file_type),
    INDEX idx_file_templates_status (status),

    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件模板表';
```

### 3.4 网站管理和回填相关表

#### 3.4.1 网站配置表 (websites)

```sql
CREATE TABLE websites (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '网站ID',
    name VARCHAR(200) NOT NULL COMMENT '网站名称',
    code VARCHAR(50) UNIQUE COMMENT '网站编码',
    url VARCHAR(500) NOT NULL COMMENT '网站URL',
    description TEXT COMMENT '网站描述',
    auth_type VARCHAR(50) NOT NULL COMMENT '认证类型：password-密码，token-令牌，oauth-OAuth，cookie-Cookie',
    auth_config JSON COMMENT '认证配置',
    selectors JSON COMMENT '页面元素选择器配置',
    automation_script TEXT COMMENT '自动化脚本',
    browser_type VARCHAR(50) DEFAULT 'chromium' COMMENT '浏览器类型：chromium,firefox,webkit',
    timeout_seconds INT DEFAULT 30 COMMENT '超时时间(秒)',
    retry_count INT DEFAULT 3 COMMENT '重试次数',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-激活，inactive-禁用，testing-测试中',
    success_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '成功率',
    avg_execution_time INT DEFAULT 0 COMMENT '平均执行时间(毫秒)',
    last_test_at DATETIME COMMENT '最后测试时间',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_websites_code (code),
    INDEX idx_websites_status (status),
    INDEX idx_websites_creator (creator_id),
    INDEX idx_websites_created (created_at),

    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站配置表';
```

#### 3.4.2 网站配置项表 (website_configs)

```sql
CREATE TABLE website_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    website_id BIGINT NOT NULL COMMENT '网站ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(50) DEFAULT 'string' COMMENT '配置类型：string,number,boolean,json',
    description TEXT COMMENT '配置描述',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY uk_website_config (website_id, config_key),
    INDEX idx_website_configs_website (website_id),

    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站配置项表';
```

#### 3.4.3 字段映射表 (field_mappings)

```sql
CREATE TABLE field_mappings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '映射ID',
    agent_id BIGINT NOT NULL COMMENT 'Agent ID',
    website_id BIGINT NOT NULL COMMENT '网站ID',
    source_field VARCHAR(100) NOT NULL COMMENT '源字段名',
    target_field VARCHAR(100) NOT NULL COMMENT '目标字段名',
    target_selector VARCHAR(500) COMMENT '目标元素选择器',
    field_type VARCHAR(50) DEFAULT 'text' COMMENT '字段类型：text,select,checkbox,radio,file',
    transform_rule JSON COMMENT '转换规则',
    validation_rule JSON COMMENT '验证规则',
    default_value VARCHAR(500) COMMENT '默认值',
    is_required BOOLEAN DEFAULT FALSE COMMENT '是否必填',
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-激活，inactive-禁用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY uk_field_mapping (agent_id, website_id, source_field),
    INDEX idx_field_mappings_agent (agent_id),
    INDEX idx_field_mappings_website (website_id),
    INDEX idx_field_mappings_status (status),

    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字段映射表';
```

#### 3.4.4 回填记录表 (fillback_records)

```sql
CREATE TABLE fillback_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '回填记录ID',
    processing_record_id BIGINT NOT NULL COMMENT '处理记录ID',
    website_id BIGINT NOT NULL COMMENT '网站ID',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态：pending-待处理，processing-处理中，completed-已完成，failed-失败',
    fillback_data JSON NOT NULL COMMENT '回填数据',
    mapped_data JSON COMMENT '映射后数据',
    execution_result JSON COMMENT '执行结果',
    error_message TEXT COMMENT '错误信息',
    error_code VARCHAR(50) COMMENT '错误代码',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    execution_time INT COMMENT '执行时间(毫秒)',
    browser_type VARCHAR(50) COMMENT '浏览器类型',
    browser_version VARCHAR(100) COMMENT '浏览器版本',
    screenshot_path VARCHAR(500) COMMENT '截图路径',
    video_path VARCHAR(500) COMMENT '录屏路径',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    started_at DATETIME COMMENT '开始时间',
    completed_at DATETIME COMMENT '完成时间',

    INDEX idx_fillback_records_processing (processing_record_id),
    INDEX idx_fillback_records_website (website_id),
    INDEX idx_fillback_records_status (status),
    INDEX idx_fillback_records_created (created_at),

    FOREIGN KEY (processing_record_id) REFERENCES processing_records(id) ON DELETE CASCADE,
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回填记录表';
```

#### 3.4.5 回填日志表 (fillback_logs)

```sql
CREATE TABLE fillback_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    fillback_record_id BIGINT NOT NULL COMMENT '回填记录ID',
    step_name VARCHAR(100) NOT NULL COMMENT '步骤名称',
    step_order INT NOT NULL COMMENT '步骤顺序',
    action_type VARCHAR(50) COMMENT '操作类型：navigate,click,type,select,submit,wait',
    target_selector VARCHAR(500) COMMENT '目标选择器',
    action_data JSON COMMENT '操作数据',
    status VARCHAR(20) NOT NULL COMMENT '状态：started-开始，completed-完成，failed-失败',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    duration INT COMMENT '耗时(毫秒)',
    error_message TEXT COMMENT '错误信息',
    screenshot_path VARCHAR(500) COMMENT '步骤截图路径',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_fillback_logs_record (fillback_record_id),
    INDEX idx_fillback_logs_step (step_name),
    INDEX idx_fillback_logs_status (status),
    INDEX idx_fillback_logs_created (created_at),

    FOREIGN KEY (fillback_record_id) REFERENCES fillback_records(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回填日志表';
```

### 3.5 系统管理相关表

#### 3.5.1 系统配置表 (system_configs)

```sql
CREATE TABLE system_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(50) DEFAULT 'string' COMMENT '配置类型：string,number,boolean,json',
    config_group VARCHAR(100) COMMENT '配置分组',
    description TEXT COMMENT '配置描述',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统配置',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-激活，inactive-禁用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_system_configs_group (config_group),
    INDEX idx_system_configs_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';
```

#### 3.5.2 操作日志表 (operation_logs)

```sql
CREATE TABLE operation_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id BIGINT COMMENT '用户ID',
    username VARCHAR(50) COMMENT '用户名',
    operation VARCHAR(100) NOT NULL COMMENT '操作类型',
    method VARCHAR(10) COMMENT '请求方法',
    request_url VARCHAR(500) COMMENT '请求URL',
    request_params TEXT COMMENT '请求参数',
    response_data TEXT COMMENT '响应数据',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    location VARCHAR(200) COMMENT '地理位置',
    user_agent TEXT COMMENT '用户代理',
    browser VARCHAR(100) COMMENT '浏览器',
    os VARCHAR(100) COMMENT '操作系统',
    status VARCHAR(20) DEFAULT 'success' COMMENT '状态：success-成功，failed-失败',
    error_message TEXT COMMENT '错误信息',
    execution_time INT COMMENT '执行时间(毫秒)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_operation_logs_user (user_id),
    INDEX idx_operation_logs_operation (operation),
    INDEX idx_operation_logs_status (status),
    INDEX idx_operation_logs_created (created_at),
    INDEX idx_operation_logs_ip (ip_address),

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';
```

#### 3.5.3 登录日志表 (login_logs)

```sql
CREATE TABLE login_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id BIGINT COMMENT '用户ID',
    username VARCHAR(50) COMMENT '用户名',
    login_type VARCHAR(50) NOT NULL COMMENT '登录类型：password-密码，sms-短信，wechat-微信',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    location VARCHAR(200) COMMENT '地理位置',
    user_agent TEXT COMMENT '用户代理',
    browser VARCHAR(100) COMMENT '浏览器',
    os VARCHAR(100) COMMENT '操作系统',
    device_type VARCHAR(50) COMMENT '设备类型：pc,mobile,tablet',
    status VARCHAR(20) NOT NULL COMMENT '状态：success-成功，failed-失败',
    failure_reason VARCHAR(200) COMMENT '失败原因',
    session_id VARCHAR(100) COMMENT '会话ID',
    logout_time DATETIME COMMENT '登出时间',
    session_duration INT COMMENT '会话时长(秒)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_login_logs_user (user_id),
    INDEX idx_login_logs_username (username),
    INDEX idx_login_logs_status (status),
    INDEX idx_login_logs_created (created_at),
    INDEX idx_login_logs_ip (ip_address),

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表';
```

#### 3.5.4 通知消息表 (notifications)

```sql
CREATE TABLE notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '通知ID',
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    type VARCHAR(50) NOT NULL COMMENT '通知类型：system-系统，task-任务，alert-告警',
    level VARCHAR(20) DEFAULT 'info' COMMENT '通知级别：info-信息，warning-警告，error-错误',
    target_type VARCHAR(50) NOT NULL COMMENT '目标类型：all-全部，role-角色，user-用户',
    target_ids TEXT COMMENT '目标ID列表，逗号分隔',
    sender_id BIGINT COMMENT '发送者ID',
    send_time DATETIME COMMENT '发送时间',
    expire_time DATETIME COMMENT '过期时间',
    status VARCHAR(20) DEFAULT 'draft' COMMENT '状态：draft-草稿，sent-已发送，expired-已过期',
    read_count INT DEFAULT 0 COMMENT '已读数量',
    total_count INT DEFAULT 0 COMMENT '总数量',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_notifications_type (type),
    INDEX idx_notifications_level (level),
    INDEX idx_notifications_status (status),
    INDEX idx_notifications_created (created_at),

    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知消息表';
```

#### 3.5.5 用户通知关联表 (user_notifications)

```sql
CREATE TABLE user_notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
    notification_id BIGINT NOT NULL COMMENT '通知ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    read_time DATETIME COMMENT '阅读时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    UNIQUE KEY uk_user_notification (notification_id, user_id),
    INDEX idx_user_notifications_notification (notification_id),
    INDEX idx_user_notifications_user (user_id),
    INDEX idx_user_notifications_read (is_read),

    FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户通知关联表';
```

#### 3.5.6 系统监控表 (system_monitors)

```sql
CREATE TABLE system_monitors (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '监控ID',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    metric_value DECIMAL(15,4) NOT NULL COMMENT '指标值',
    metric_unit VARCHAR(20) COMMENT '指标单位',
    metric_type VARCHAR(50) NOT NULL COMMENT '指标类型：cpu,memory,disk,network,business',
    instance_name VARCHAR(100) COMMENT '实例名称',
    tags JSON COMMENT '标签',
    timestamp DATETIME NOT NULL COMMENT '时间戳',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_system_monitors_metric (metric_name),
    INDEX idx_system_monitors_type (metric_type),
    INDEX idx_system_monitors_timestamp (timestamp),
    INDEX idx_system_monitors_instance (instance_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统监控表';
```

## 4. 数据库索引优化策略

### 4.1 主要索引设计

#### 4.1.1 复合索引

```sql
-- 用户相关复合索引
CREATE INDEX idx_users_dept_status ON users(department_id, status);
CREATE INDEX idx_users_status_created ON users(status, created_at);

-- Agent相关复合索引
CREATE INDEX idx_agents_creator_status ON agents(creator_id, status);
CREATE INDEX idx_agents_category_status ON agents(category_id, status);
CREATE INDEX idx_agents_model_status ON agents(llm_model_id, status);

-- 处理记录复合索引
CREATE INDEX idx_processing_user_agent ON processing_records(user_id, agent_id);
CREATE INDEX idx_processing_status_created ON processing_records(status, created_at);
CREATE INDEX idx_processing_agent_created ON processing_records(agent_id, created_at);

-- 回填记录复合索引
CREATE INDEX idx_fillback_website_status ON fillback_records(website_id, status);
CREATE INDEX idx_fillback_status_created ON fillback_records(status, created_at);

-- 日志相关复合索引
CREATE INDEX idx_operation_logs_user_created ON operation_logs(user_id, created_at);
CREATE INDEX idx_login_logs_user_created ON login_logs(user_id, created_at);
```

#### 4.1.2 分区表设计

```sql
-- 按月分区的处理记录表
CREATE TABLE processing_records_partitioned (
    LIKE processing_records INCLUDING ALL
) PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    PARTITION p202404 VALUES LESS THAN (202405),
    PARTITION p202405 VALUES LESS THAN (202406),
    PARTITION p202406 VALUES LESS THAN (202407),
    PARTITION p202407 VALUES LESS THAN (202408),
    PARTITION p202408 VALUES LESS THAN (202409),
    PARTITION p202409 VALUES LESS THAN (202410),
    PARTITION p202410 VALUES LESS THAN (202411),
    PARTITION p202411 VALUES LESS THAN (202412),
    PARTITION p202412 VALUES LESS THAN (202501),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 按月分区的日志表
CREATE TABLE operation_logs_partitioned (
    LIKE operation_logs INCLUDING ALL
) PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 4.2 查询优化建议

#### 4.2.1 常用查询优化

```sql
-- 1. 用户Agent列表查询优化
-- 原查询
SELECT a.*, c.name as category_name
FROM agents a
LEFT JOIN agent_categories c ON a.category_id = c.id
WHERE a.creator_id = ? AND a.status = 'published'
ORDER BY a.created_at DESC;

-- 优化：添加覆盖索引
CREATE INDEX idx_agents_creator_status_created ON agents(creator_id, status, created_at, name, category_id);

-- 2. 处理记录统计查询优化
-- 原查询
SELECT DATE(created_at) as date, COUNT(*) as count,
       SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as success_count
FROM processing_records
WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at);

-- 优化：添加复合索引
CREATE INDEX idx_processing_user_created_status ON processing_records(user_id, created_at, status);

-- 3. 回填成功率统计优化
-- 原查询
SELECT w.name,
       COUNT(*) as total,
       SUM(CASE WHEN fr.status = 'completed' THEN 1 ELSE 0 END) as success
FROM fillback_records fr
JOIN websites w ON fr.website_id = w.id
WHERE fr.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY w.id, w.name;

-- 优化：添加复合索引
CREATE INDEX idx_fillback_created_website_status ON fillback_records(created_at, website_id, status);
```

## 5. 数据库初始化数据

### 5.1 系统基础数据

#### 5.1.1 角色权限初始化

```sql
-- 插入系统角色
INSERT INTO roles (name, code, description, status) VALUES
('系统管理员', 'ADMIN', '系统管理员，拥有所有权限', 'active'),
('航空代理员工', 'AGENT_USER', '航空代理员工，负责Agent管理和使用', 'active'),
('普通用户', 'USER', '普通用户，只能使用Agent', 'active');

-- 插入系统权限
INSERT INTO permissions (name, code, resource, action, description) VALUES
-- 用户管理权限
('用户查看', 'user:read', 'user', 'read', '查看用户信息'),
('用户创建', 'user:create', 'user', 'create', '创建用户'),
('用户编辑', 'user:update', 'user', 'update', '编辑用户信息'),
('用户删除', 'user:delete', 'user', 'delete', '删除用户'),

-- Agent管理权限
('Agent查看', 'agent:read', 'agent', 'read', '查看Agent'),
('Agent创建', 'agent:create', 'agent', 'create', '创建Agent'),
('Agent编辑', 'agent:update', 'agent', 'update', '编辑Agent'),
('Agent删除', 'agent:delete', 'agent', 'delete', '删除Agent'),
('Agent发布', 'agent:publish', 'agent', 'publish', '发布Agent'),

-- 文件处理权限
('文件上传', 'file:upload', 'file', 'upload', '上传文件'),
('文件处理', 'file:process', 'file', 'process', '处理文件'),
('结果查看', 'result:read', 'result', 'read', '查看处理结果'),

-- 网站管理权限
('网站查看', 'website:read', 'website', 'read', '查看网站配置'),
('网站创建', 'website:create', 'website', 'create', '创建网站配置'),
('网站编辑', 'website:update', 'website', 'update', '编辑网站配置'),
('网站删除', 'website:delete', 'website', 'delete', '删除网站配置'),

-- 回填权限
('回填执行', 'fillback:execute', 'fillback', 'execute', '执行回填操作'),
('回填查看', 'fillback:read', 'fillback', 'read', '查看回填记录'),

-- 系统管理权限
('系统配置', 'system:config', 'system', 'config', '系统配置管理'),
('日志查看', 'log:read', 'log', 'read', '查看系统日志'),
('监控查看', 'monitor:read', 'monitor', 'read', '查看系统监控');
```

#### 5.1.2 LLM模型初始化

```sql
-- 插入LLM模型
INSERT INTO llm_models (name, code, provider, model_version, api_endpoint, max_tokens, temperature, top_p, supports_vision, supports_function_call, cost_per_1k_tokens, status, description) VALUES
('GPT-4 Vision', 'gpt-4-vision-preview', 'openai', 'gpt-4-vision-preview', 'https://api.openai.com/v1/chat/completions', 4096, 0.7, 1.0, TRUE, TRUE, 0.01, 'active', 'OpenAI GPT-4 with vision capabilities'),
('GPT-4 Turbo', 'gpt-4-turbo-preview', 'openai', 'gpt-4-turbo-preview', 'https://api.openai.com/v1/chat/completions', 128000, 0.7, 1.0, FALSE, TRUE, 0.01, 'active', 'OpenAI GPT-4 Turbo'),
('Claude-3 Opus', 'claude-3-opus', 'anthropic', 'claude-3-opus-20240229', 'https://api.anthropic.com/v1/messages', 200000, 0.7, 1.0, TRUE, FALSE, 0.015, 'active', 'Anthropic Claude-3 Opus with vision'),
('文心一言', 'ernie-bot-4', 'baidu', 'ERNIE-Bot-4', 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro', 8192, 0.7, 1.0, FALSE, TRUE, 0.008, 'active', '百度文心一言4.0'),
('通义千问', 'qwen-max', 'alibaba', 'qwen-max', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', 8192, 0.7, 1.0, FALSE, TRUE, 0.008, 'active', '阿里通义千问Max版本');
```

#### 5.1.3 Agent分类初始化

```sql
-- 插入Agent分类
INSERT INTO agent_categories (name, code, description, icon, color, sort_order, status) VALUES
('航空运单', 'AWB', '航空运单(Air Waybill)识别', 'airplane', '#1890ff', 1, 'active'),
('商业发票', 'INVOICE', '商业发票(Commercial Invoice)识别', 'file-text', '#52c41a', 2, 'active'),
('装箱单', 'PACKING_LIST', '装箱单(Packing List)识别', 'container', '#fa8c16', 3, 'active'),
('提单', 'BILL_OF_LADING', '提单(Bill of Lading)识别', 'ship', '#722ed1', 4, 'active'),
('报关单', 'CUSTOMS_DECLARATION', '报关单识别', 'bank', '#eb2f96', 5, 'active'),
('证书类', 'CERTIFICATE', '各类证书识别', 'safety-certificate', '#13c2c2', 6, 'active'),
('其他单证', 'OTHER', '其他类型单证识别', 'file', '#666666', 99, 'active');
```

#### 5.1.4 系统配置初始化

```sql
-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, config_type, config_group, description, is_system, status) VALUES
-- 系统基础配置
('system.name', 'Sinoair Agent', 'string', 'system', '系统名称', TRUE, 'active'),
('system.version', '1.0.0', 'string', 'system', '系统版本', TRUE, 'active'),
('system.logo', '/assets/logo.png', 'string', 'system', '系统Logo', FALSE, 'active'),
('system.timezone', 'Asia/Shanghai', 'string', 'system', '系统时区', FALSE, 'active'),

-- 文件上传配置
('file.max_size', '********', 'number', 'file', '文件最大大小(字节)', FALSE, 'active'),
('file.allowed_types', 'pdf,jpg,jpeg,png,tiff,bmp', 'string', 'file', '允许的文件类型', FALSE, 'active'),
('file.storage_path', '/data/uploads', 'string', 'file', '文件存储路径', FALSE, 'active'),

-- 处理配置
('processing.max_concurrent', '10', 'number', 'processing', '最大并发处理数', FALSE, 'active'),
('processing.timeout_seconds', '300', 'number', 'processing', '处理超时时间(秒)', FALSE, 'active'),
('processing.retry_count', '3', 'number', 'processing', '重试次数', FALSE, 'active'),

-- 回填配置
('fillback.timeout_seconds', '60', 'number', 'fillback', '回填超时时间(秒)', FALSE, 'active'),
('fillback.retry_count', '3', 'number', 'fillback', '回填重试次数', FALSE, 'active'),
('fillback.screenshot_enabled', 'true', 'boolean', 'fillback', '是否启用截图', FALSE, 'active'),

-- 安全配置
('security.password_min_length', '8', 'number', 'security', '密码最小长度', FALSE, 'active'),
('security.login_max_attempts', '5', 'number', 'security', '登录最大尝试次数', FALSE, 'active'),
('security.session_timeout', '86400', 'number', 'security', '会话超时时间(秒)', FALSE, 'active');
```

## 6. 数据库维护策略

### 6.1 数据清理策略

#### 6.1.1 定期清理脚本

```sql
-- 清理30天前的处理日志
DELETE FROM processing_logs
WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理90天前的操作日志
DELETE FROM operation_logs
WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 清理180天前的登录日志
DELETE FROM login_logs
WHERE created_at < DATE_SUB(NOW(), INTERVAL 180 DAY);

-- 清理过期的通知消息
DELETE FROM notifications
WHERE expire_time < NOW() AND status = 'expired';

-- 清理失败的处理记录(保留30天)
DELETE FROM processing_records
WHERE status = 'failed' AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

#### 6.1.2 数据归档策略

```sql
-- 创建归档表
CREATE TABLE processing_records_archive LIKE processing_records;
CREATE TABLE operation_logs_archive LIKE operation_logs;

-- 归档一年前的数据
INSERT INTO processing_records_archive
SELECT * FROM processing_records
WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);

-- 删除已归档的数据
DELETE FROM processing_records
WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);
```

### 6.2 性能监控

#### 6.2.1 慢查询监控

```sql
-- 开启慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
SET GLOBAL log_queries_not_using_indexes = 'ON';

-- 查看慢查询统计
SELECT
    SCHEMA_NAME,
    DIGEST_TEXT,
    COUNT_STAR,
    AVG_TIMER_WAIT/1000000000 AS avg_time_seconds,
    MAX_TIMER_WAIT/1000000000 AS max_time_seconds
FROM performance_schema.events_statements_summary_by_digest
WHERE SCHEMA_NAME = 'sinoair_agent'
ORDER BY AVG_TIMER_WAIT DESC
LIMIT 10;
```

#### 6.2.2 表空间监控

```sql
-- 监控表大小
SELECT
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS table_size_mb,
    TABLE_ROWS
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = 'sinoair_agent'
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- 监控索引使用情况
SELECT
    OBJECT_SCHEMA,
    OBJECT_NAME,
    INDEX_NAME,
    COUNT_FETCH,
    COUNT_INSERT,
    COUNT_UPDATE,
    COUNT_DELETE
FROM performance_schema.table_io_waits_summary_by_index_usage
WHERE OBJECT_SCHEMA = 'sinoair_agent'
ORDER BY COUNT_FETCH DESC;
```

## 7. 总结

### 7.1 数据库设计特点

1. **完整性**：涵盖了Sinoair Agent系统的所有核心功能模块
2. **规范性**：遵循数据库设计规范，字段命名统一，注释完整
3. **扩展性**：预留了扩展字段，支持系统功能的持续迭代
4. **性能性**：合理设计索引，支持高并发访问
5. **安全性**：敏感数据加密存储，完善的权限控制
6. **可维护性**：清晰的表结构关系，便于后期维护

### 7.2 核心表统计


| 模块      | 表数量     | 核心表                                        |
| --------- | ---------- | --------------------------------------------- |
| 用户管理  | 6张        | users, roles, permissions                     |
| Agent管理 | 7张        | agents, agent_versions, llm_models            |
| 文件处理  | 4张        | processing_records, processing_batches        |
| 网站回填  | 5张        | websites, fillback_records, field_mappings    |
| 系统管理  | 6张        | system_configs, operation_logs, notifications |
| **总计**  | **28张** | **核心业务表15张**                          |

### 7.3 实施建议

1. **分阶段实施**：按模块逐步创建表结构，降低实施风险
2. **数据迁移**：制定详细的数据迁移计划，确保数据安全
3. **性能测试**：在生产环境部署前进行充分的性能测试
4. **监控告警**：建立完善的数据库监控和告警机制
5. **备份策略**：制定定期备份和灾难恢复方案

本数据库设计为Sinoair Agent系统提供了坚实的数据基础，支持系统的稳定运行和持续发展。

```

```

```

```

```

```
